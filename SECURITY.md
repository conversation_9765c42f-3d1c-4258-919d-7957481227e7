# دليل الأمان - نظام المصادقة

## 🔐 الميزات الأمنية المطبقة

### 1. إدارة التوكن (Token Management)
- **تخزين آمن**: يتم حفظ `accessToken` في `localStorage`
- **انتهاء الصلاحية**: التوكن له مدة صلاحية محددة (15 دقيقة)
- **تنظيف تلقائي**: مسح التوكن عند تسجيل الخروج أو انتهاء الصلاحية

### 2. Axios Interceptors
- **طلبات**: إضافة التوكن تلقائياً لجميع الطلبات
- **استجابات**: التعامل مع أخطاء 401 وإعادة التوجيه للتسجيل

### 3. حماية المسارات
- **Dashboard**: محمي للأدمن فقط
- **التحقق من الصلاحيات**: فحص دور المستخدم قبل الوصول

### 4. التحقق من البيانات
- **React Hook Form**: التحقق من صحة البيانات
- **Zod Schema**: التحقق من نوع البيانات
- **تشفير كلمات المرور**: في الخادم

## 🛡️ إجراءات الأمان

### إعادة تعيين كلمة المرور
1. **طلب إعادة التعيين**: إرسال البريد الإلكتروني
2. **رمز التحقق**: استلام رمز آمن عبر البريد
3. **التحقق**: استخدام الرمز لتأكيد الهوية
4. **تحديث كلمة المرور**: مع التحقق من التطابق

### إدارة الجلسات
- **تسجيل الدخول**: حفظ بيانات المستخدم والتوكن
- **تسجيل الخروج**: مسح جميع البيانات المحلية
- **انتهاء الجلسة**: إعادة توجيه تلقائية للتسجيل

## 🚀 جاهز للإنتاج

### متطلبات الخادم
- **HTTPS**: إجباري في الإنتاج
- **CORS**: تكوين صحيح للنطاقات المسموحة
- **Rate Limiting**: حد أقصى للطلبات
- **تشفير البيانات**: في قاعدة البيانات

### متغيرات البيئة
```env
REACT_APP_API_URL=https://api.nafs-baserah.com/api/
REACT_APP_ENVIRONMENT=production
```

### نصائح الأمان
1. **لا تعرض التوكن**: في console أو logs
2. **استخدم HTTPS**: في جميع الطلبات
3. **تحديث التبعيات**: بانتظام
4. **مراقبة الأخطاء**: نظام تتبع الأخطاء

## 📋 قائمة التحقق قبل النشر

- [ ] تفعيل HTTPS
- [ ] تكوين CORS
- [ ] إخفاء console.log في الإنتاج
- [ ] تفعيل Rate Limiting
- [ ] اختبار جميع المسارات
- [ ] التحقق من إدارة الأخطاء
- [ ] اختبار انتهاء صلاحية التوكن
- [ ] التأكد من حماية المسارات الحساسة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة
1. **401 Unauthorized**: التوكن منتهي الصلاحية
2. **403 Forbidden**: المستخدم ليس له صلاحية
3. **Network Error**: مشكلة في الاتصال بالخادم

### الحلول
- تحديث التوكن تلقائياً
- إعادة تسجيل الدخول
- التحقق من حالة الشبكة
