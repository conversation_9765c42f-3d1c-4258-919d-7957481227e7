interface cardServPropType
{
    flexDer : string; 
    servImage : string ;

}
export default function ServicesCard({flexDer , servImage} : cardServPropType) {
  return (
    <div className={`flex rounded-3xl z-50 ${flexDer} overflow-hidden bg-white 2xl:min-h-[524px] 2xl:w-[43.0729%]`}>
      <div className=" p-7 h-full w-[45%]">

      </div>
      <div className=" w-[55%] h-full bg-black">
        <img src={servImage} className=" w-full h-full" alt="" />
      </div>
    </div>
  )
}
