import { NavLink } from "react-router-dom";
import { NavData } from "../../../Data/NavData";
import { FaAngleDown } from "react-icons/fa";

export default function LinkUl() {
  return (
    <ul className="hidden lg:flex xl:gap-4.5 lg:gap-3    items-center ">
        {NavData.map((item, index) => (
        <li key={index} className="flex xl:text-xl text-base lg:font-semibold items-center gap-1">
            <NavLink to={item.path} end className={({ isActive }) => `flex h-[42px] items-center justify-center rounded-sm transition ${
                isActive
                ? "bg-[#F5F5F5] xl:w-[158px] lg:w-[80px] text-[#7E3994]  "
                : "text-white xl:w-[120px] xl:hover:w-[114px] lg:hover:w-[80px] transition-all duration-200  hover:bg-[#F5F5F5] hover:text-[#7E3994] "}`}>
                    {item.name}
                {index === 2 && <FaAngleDown className="mt-1 mx-1" />}
            </NavLink>
        </li>))}
    </ul>
  )
}
