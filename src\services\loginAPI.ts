import { api } from "./axios";

interface User {
  _id: string;
  fullName: string;
  email: string;
  role: 'user' | 'admin';
}

interface LoginResponse {
  status: string;
  message: string;
  data: {
    accessToken: string;
    user: User;
  };
}

interface LoginData {
  email: string;
  password: string;
}

interface SignUpData
{
    fullName : string;
    email : string;
    password : string;
    confirmPassword : string;
    phoneNumber : string;
}
interface resetParams
{
    email : string;
}
export async function login(data: LoginData): Promise<LoginResponse> {
  const res = await api.post("/auth/login", data);

  // حفظ التوكن في localStorage
  if (res.data.data?.accessToken) {
    localStorage.setItem('accessToken', res.data.data.accessToken);
  }

  return res.data;
}

export async function signUp(data: SignUpData) {
  const res = await api.post("/auth/register", data);
  return res.data;
}

export async function getCurrentUser(): Promise<User> {
  const token = localStorage.getItem('accessToken');
  if (!token) {
    throw new Error('No access token found');
  }

  const res = await api.get("/auth/me", {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
  return res.data.user;
}

export async function logout() {
  const token = localStorage.getItem('accessToken');

  try {
    await api.post("/auth/logout", {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  } finally {
    // مسح التوكن من localStorage في جميع الحالات
    localStorage.removeItem('accessToken');
  }
}
export async function resetPass(userData: resetParams) {
  const res = await api.post("/auth/forgot-password", userData);
  return res.data;
}

export async function resetPassword(data: {
  resetCode: string;
  email: string;
  password: string;
  confirmPassword: string;
}) {
  const res = await api.post("/auth/reset-password", data);
  return res.data;
}
