import { api } from "./axios";
import { config, secureLog } from "../config/environment";

interface User {
  _id: string;
  fullName: string;
  email: string;
  role: 'user' | 'admin';
}

interface LoginResponse {
  status: string;
  message: string;
  data: {
    user: User;
    accessToken?: string; // قد يكون موجود أو لا
  };
}

interface LoginData {
  email: string;
  password: string;
}

interface SignUpData
{
    fullName : string;
    email : string;
    password : string;
    confirmPassword : string;
    phoneNumber : string;
}
interface resetParams
{
    email : string;
}
export async function login(data: LoginData): Promise<LoginResponse> {
  secureLog("Attempting login", { email: data.email });
  const res = await api.post("/auth/login", data);

  // طباعة الاستجابة للتحقق من البنية
  secureLog("Login response", res.data);

  // في هذه الحالة، الخادم لا يرسل التوكن، لذا سنحفظ بيانات المستخدم مؤقتاً
  if (res.data.status === "success" && res.data.data?.user) {
    // حفظ بيانات المستخدم في localStorage كحل مؤقت
    localStorage.setItem('currentUser', JSON.stringify(res.data.data.user));
    secureLog("Login successful, user data saved", res.data.data.user);
  }

  return res.data;
}

export async function signUp(data: SignUpData) {
  const res = await api.post("/auth/register", data);
  return res.data;
}

export async function getCurrentUser(): Promise<User> {
  // أولاً، نحاول قراءة بيانات المستخدم من localStorage
  const savedUser = localStorage.getItem('currentUser');
  if (savedUser) {
    try {
      const user = JSON.parse(savedUser);
      secureLog("User loaded from localStorage", user);
      return user;
    } catch (error) {
      secureLog("Error parsing saved user", error);
      localStorage.removeItem('currentUser');
    }
  }

  // إذا لم تكن هناك بيانات محفوظة، نحاول جلبها من الخادم
  const token = localStorage.getItem(config.token.storageKey);
  if (!token) {
    secureLog("No access token found and no saved user");
    throw new Error('No access token found');
  }

  secureLog("Fetching current user from server");

  try {
    const res = await api.get("/auth/me");

    secureLog("Current user response from server", res.data);

    // التعامل مع بنية البيانات المختلفة
    let user: User;
    if (res.data.user) {
      user = res.data.user;
    } else if (res.data.data?.user) {
      user = res.data.data.user;
    } else if (res.data._id) {
      user = res.data;
    } else {
      throw new Error('User data not found in response');
    }

    // حفظ بيانات المستخدم في localStorage
    localStorage.setItem('currentUser', JSON.stringify(user));
    return user;

  } catch (error) {
    secureLog("Error fetching current user from server", error);
    throw error;
  }
}

export async function logout() {
  const token = localStorage.getItem(config.token.storageKey);

  secureLog("Attempting logout");

  try {
    if (token) {
      await api.post("/auth/logout", {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
    }
  } catch (error) {
    secureLog("Logout API call failed", error);
  } finally {
    // مسح جميع البيانات من localStorage
    localStorage.removeItem(config.token.storageKey);
    localStorage.removeItem('currentUser');
    secureLog("All user data removed from localStorage");
  }
}
export async function resetPass(userData: resetParams) {
  const res = await api.post("/auth/forgot-password", userData);
  return res.data;
}

export async function resetPassword(data: {
  resetCode: string;
  email: string;
  password: string;
  confirmPassword: string;
}) {
  const res = await api.post("/auth/reset-password", data);
  return res.data;
}
