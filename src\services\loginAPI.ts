import { api } from "./axios";
import { config, secureLog } from "../config/environment";

interface User {
  _id: string;
  fullName: string;
  email: string;
  role: 'user' | 'admin';
}

interface LoginResponse {
  status: string;
  message: string;
  data: {
    accessToken: string;
    user: User;
  };
}

interface LoginData {
  email: string;
  password: string;
}

interface SignUpData
{
    fullName : string;
    email : string;
    password : string;
    confirmPassword : string;
    phoneNumber : string;
}
interface resetParams
{
    email : string;
}
export async function login(data: LoginData): Promise<LoginResponse> {
  secureLog("Attempting login", { email: data.email });
  const res = await api.post("/auth/login", data);

  // حفظ التوكن في localStorage
  if (res.data.data?.accessToken) {
    localStorage.setItem(config.token.storageKey, res.data.data.accessToken);
    secureLog("Login successful, token saved");
  }

  return res.data;
}

export async function signUp(data: SignUpData) {
  const res = await api.post("/auth/register", data);
  return res.data;
}

export async function getCurrentUser(): Promise<User> {
  const token = localStorage.getItem(config.token.storageKey);
  if (!token) {
    secureLog("No access token found");
    throw new Error('No access token found');
  }

  secureLog("Fetching current user");
  const res = await api.get("/auth/me", {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  secureLog("Current user fetched successfully");
  return res.data.user;
}

export async function logout() {
  const token = localStorage.getItem(config.token.storageKey);

  secureLog("Attempting logout");

  try {
    if (token) {
      await api.post("/auth/logout", {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
    }
  } catch (error) {
    secureLog("Logout API call failed", error);
  } finally {
    // مسح التوكن من localStorage في جميع الحالات
    localStorage.removeItem(config.token.storageKey);
    secureLog("Token removed from localStorage");
  }
}
export async function resetPass(userData: resetParams) {
  const res = await api.post("/auth/forgot-password", userData);
  return res.data;
}

export async function resetPassword(data: {
  resetCode: string;
  email: string;
  password: string;
  confirmPassword: string;
}) {
  const res = await api.post("/auth/reset-password", data);
  return res.data;
}
