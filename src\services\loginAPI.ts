import { api } from "./axios";

interface User {
  id: string;
  fullName: string;
  email: string;
  role: 'user' | 'admin';
}

interface LoginData {
  email: string;
  password: string;
}

interface SignUpData
{
    fullName : string;
    email : string;
    password : string;
    confirmPassword : string;
    phoneNumber : string;
}
interface resetParams
{
    email : string;
}
export async function login(data: LoginData) {
  // backend يحط الكوكي
  const res = await api.post("/auth/login", data);
  return res.data as { user: User };
}

export async function signUp(data: SignUpData) {
  const res = await api.post("/auth/register", data);
  return res.data as { user: User };
}


export async function logout() {
  const res = await api.post("/auth/logout");
  return res.data;
}
export async function resetPass(userData :resetParams) {
  const res = await api.post("/auth/forgot-password" , userData);
  return res.data ;

}
