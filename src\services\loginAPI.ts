import { api } from "./axios";
import { config, secureLog } from "../config/environment";

interface User {
  _id: string;
  fullName: string;
  email: string;
  role: 'user' | 'admin';
}

interface LoginResponse {
  status: string;
  message: string;
  data: {
    accessToken: string;
    user: User;
  };
}

interface LoginData {
  email: string;
  password: string;
}

interface SignUpData
{
    fullName : string;
    email : string;
    password : string;
    confirmPassword : string;
    phoneNumber : string;
}
interface resetParams
{
    email : string;
}
export async function login(data: LoginData): Promise<LoginResponse> {
  secureLog("Attempting login", { email: data.email });
  const res = await api.post("/auth/login", data);

  // طباعة الاستجابة للتحقق من البنية
  secureLog("Login response", res.data);

  // حفظ التوكن في localStorage
  if (res.data.data?.accessToken) {
    localStorage.setItem(config.token.storageKey, res.data.data.accessToken);
    secureLog("Login successful, token saved");
  } else if (res.data.accessToken) {
    // في حالة كان التوكن في المستوى الأول
    localStorage.setItem(config.token.storageKey, res.data.accessToken);
    secureLog("Login successful, token saved (alternative structure)");
  }

  return res.data;
}

export async function signUp(data: SignUpData) {
  const res = await api.post("/auth/register", data);
  return res.data;
}

export async function getCurrentUser(): Promise<User> {
  const token = localStorage.getItem(config.token.storageKey);
  if (!token) {
    secureLog("No access token found");
    throw new Error('No access token found');
  }

  secureLog("Fetching current user with token", { tokenExists: !!token });

  try {
    const res = await api.get("/auth/me");

    secureLog("Current user response", res.data);

    // التعامل مع بنية البيانات المختلفة
    if (res.data.user) {
      return res.data.user;
    } else if (res.data.data?.user) {
      return res.data.data.user;
    } else if (res.data._id) {
      // في حالة كانت بيانات المستخدم في المستوى الأول
      return res.data;
    }

    throw new Error('User data not found in response');
  } catch (error) {
    secureLog("Error fetching current user", error);
    throw error;
  }
}

export async function logout() {
  const token = localStorage.getItem(config.token.storageKey);

  secureLog("Attempting logout");

  try {
    if (token) {
      await api.post("/auth/logout", {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
    }
  } catch (error) {
    secureLog("Logout API call failed", error);
  } finally {
    // مسح التوكن من localStorage في جميع الحالات
    localStorage.removeItem(config.token.storageKey);
    secureLog("Token removed from localStorage");
  }
}
export async function resetPass(userData: resetParams) {
  const res = await api.post("/auth/forgot-password", userData);
  return res.data;
}

export async function resetPassword(data: {
  resetCode: string;
  email: string;
  password: string;
  confirmPassword: string;
}) {
  const res = await api.post("/auth/reset-password", data);
  return res.data;
}
