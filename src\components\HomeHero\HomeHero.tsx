import woman from '../../assets/hero/hero-woman.webp'
import { buttonDtatHero } from '../../Data/buttonHero'
import Button from '../ui/Button/Button'
import HeroButton from '../ui/Button/HeroButton'
import HeroSubTitle from '../ui/Titles/HeroSubTitle'
import HeroTitle from '../ui/Titles/HeroTitle'
export default function HomeHero() {
  return (
    <section className="h-screen w-full padding-global
    lg:static relative   lg:justify-start justify-center items-center lg:flex-row flex-col-reverse  lg:items-end bg-top  bg-no-repeat lg:gap-15 2xl:gap-25  flex  "> 
      <div className='  lg:w-[30%] lg:h-[75%] w-[25%] flex lg:static absolute bottom-0 right-[5%] items-end justify-end'>
        <img  src={woman} className='  h-full w-full' alt="رضا محتسب" />
      </div>
      <div className='lg:justify-end justify-center  lg:w-[70%] w-full flex flex-col lg:items-start items-center  h-full'>
        <div className=' text-center'>
        <HeroTitle/>
        <HeroSubTitle/>
        </div>
        <div className=' flex    items-center gap-8'>
          <Button text={'احجزي استشارة'} className=' text-white bg-[#4B2C53]'/>
          <Button text={'ابدأ رحلتك معي الآن'} className=' bg-white text-[#4B2C53]'/>
        </div>
        <div className=' w-full flex lg:justify-end justify-end  lg:gap-6.5 gap-5 2xl:gap-8 lg:my-[7.5%] my-[4.5%] xl:my-[11%]'>
          {buttonDtatHero.map((button) => (
            <HeroButton key={button.label} text={button.label} icon={button.iconSrc} />
          ))}
        </div>
      </div>
    </section>
  )
}
