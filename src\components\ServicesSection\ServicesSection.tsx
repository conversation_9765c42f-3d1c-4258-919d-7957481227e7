import { ServiceData } from "../../Data/ServiceData";
import background from "../../img/service/background.webp";
import mandellaTop from "../../img/service/mandellaTop.webp";
import mandellaBottom from "../../img/service/mandellaBottom.webp";
import SectionTitle from "../ui/Titles/SectionTitle";
import ServicesCard from "../ui/Cards/ServicesCard";

export default function ServicesSection() {
  return (
    <section className=" py-12.5 relative  padding-global w-full">
      <SectionTitle text="خدماتنا" className=" mb-[56px] text-white"/>
      {ServiceData.map((e,i) => 
      (
      <div key={i} className=" flex justify-end w-full">
         <ServicesCard servImage={e.image} flexDer=" flex-row-reverse "/>

      </div>
      ))}

      <div className=" w-[62px]  rounded-t-[8px]  border-t-2 border-r-2 border-l-0 border-b-0 border-white relative top-[485px] right-[-48.5%] ">
        <div className="w-[40px] h-0.5 bg-white   absolute right-[-40px] top-[262px]"></div>
        <div className="w-0 h-0 border-t-[9.375px] border-b-[9.375px] border-l-[15px] border-t-transparent border-b-transparent border-white absolute right-[-48px] top-[254px]"></div>
        <div className="w-[40px] h-[10px] rounded-tr-[300px] border-t-2 border-r-2 border-white absolute right-[-2px] top-[768px]"></div>
        <div className="w-0 h-0 border-t-[9.375px] border-b-[9.375px] border-r-[15px] border-t-transparent border-b-transparent border-white absolute right-[30px] top-[760px]"></div>
        <div className="w-[40px] h-[10px] rounded-bl-[300px] border-l-2 border-b-2 border-white absolute left-[60px] top-[968px]"></div>
        <div className="w-0 h-0 border-t-[9.375px] border-b-[9.375px] border-l-[15px] border-t-transparent border-b-transparent border-white absolute right-[-48px] top-[968px]"></div>
      </div>

      <img
        src={background}
        alt=""
        className="w-[316px] absolute top-[324px] left-[879px]   "
      />
      <img
        src={background}
        alt=""
        className="w-[316px] absolute top-[692px] left-[0px]   "
      />
      <img
        src={background}
        alt=""
        className="w-[316px] absolute top-[1330px] left-[807px]   "
      />
      <img
        src={mandellaTop}
        alt=""
        className=" absolute top-[0px] right-[0px]   "
      />
      <img
        src={mandellaBottom}
        alt=""
        className=" absolute bottom-[0px] left-[0px]   "
      />
    </section>
  );
}
