import { useCurrentUser } from "../../hooks/useAuth";
import { useQueryClient } from "@tanstack/react-query";

export default function DebugUser() {
  const { data: user, isLoading, error, refetch } = useCurrentUser();
  const queryClient = useQueryClient();
  const token = localStorage.getItem('accessToken');

  const handleRefetch = () => {
    console.log("Manual refetch triggered");
    refetch();
  };

  const handleClearCache = () => {
    console.log("Clearing query cache");
    queryClient.clear();
  };

  if (!import.meta.env.DEV) {
    return null; // لا نعرض هذا في الإنتاج
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Debug User State:</h3>
      <div>
        <strong>Token:</strong> {token ? 'موجود' : 'غير موجود'}
      </div>
      <div>
        <strong>Loading:</strong> {isLoading ? 'نعم' : 'لا'}
      </div>
      <div>
        <strong>Error:</strong> {error ? error.message : 'لا يوجد'}
      </div>
      <div>
        <strong>User:</strong> {user ? user.fullName : 'غير موجود'}
      </div>
      <div>
        <strong>Role:</strong> {user?.role || 'غير محدد'}
      </div>
      <div className="mt-2 space-y-1">
        <button
          onClick={handleRefetch}
          className="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-1"
        >
          إعادة جلب
        </button>
        <button
          onClick={handleClearCache}
          className="bg-red-600 text-white px-2 py-1 rounded text-xs"
        >
          مسح الكاش
        </button>
      </div>
    </div>
  );
}
