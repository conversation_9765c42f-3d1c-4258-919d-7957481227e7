import React from 'react';
import { Step } from '../../types/howWork';
import Line from './../../assets/image/Line.png'
import SectionTitle from '../ui/Titles/SectionTitle';
import Card from './Card';
type HowWorkProps = {
  steps: Step[];
};

const HowWork: React.FC<HowWorkProps> = ({ steps }) => {
  return (
    <div className="bg-[#3B2241] padding-global py-12 lg:py-16 text-white">
      <SectionTitle text='كيف تعمل المنصة' className='mb-8 lg:mb-14'/>
      <div className="flex w-full flex-col lg:flex-row items-center justify-between gap-3 lg:gap-2">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            {/* العنصر الأساسي */}
             <Card stepIcon={step.icon} title={step.title} description={step.description}/>
            {/* الخط الفاصل */}
            {index < steps.length - 1 && (
              <>
                {/* خط أفقي للشاشات الكبيرة */}
                <div className="hidden lg:block">
                  <img
                    src={Line}
                    alt="line"
                    className=" laptop:h-[83px] xl:w-[170px]  laptop:w-[120px] lg:w-[95px]  2xl:w-[262px]"
                  />
                </div>

                {/* خط عمودي للشاشات الصغيرة */}
                <div className="lg:hidden w-px h-8 bg-white/30"></div>
              </>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default HowWork;
