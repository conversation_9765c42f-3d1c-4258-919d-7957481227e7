import React from 'react';
import { Step } from '../../types/howWork';
import Line from './../../assets/image/Line.png'
import SectionTitle from '../ui/Titles/SectionTitle';
import IconDev from './IconDev';
type HowWorkProps = {
  steps: Step[];
};

const HowWork: React.FC<HowWorkProps> = ({ steps }) => {
  return (
    <div className="bg-[#3B2241] padding-global py-12.5 text-white">
      <SectionTitle text='كيف تعمل المنصة' className=' mb-[56px]'/>
      <div className="flex w-full flex-col md:flex-row items-center  gap-8 ">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center w-1/4">
            <div className="flex flex-col items-center">
              <IconDev icon={step.icon}/>
              <h3 className="text-l mb-4">{step.title}</h3>
              <p className="text-sm text-center">{step.description}</p>
            </div>
            {index < steps.length - 1 && (
              <div className="hidden md:block mx-4">
                <img 
                  src={Line} 
                  alt="line" 
                  className="h-[83px] w-[262px]"
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default HowWork;
