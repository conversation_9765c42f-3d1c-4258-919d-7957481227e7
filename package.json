{"name": "consulting-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@reduxjs/toolkit": "^2.8.1", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.83.1", "axios": "^1.11.0", "country-codes-flags-phone-codes": "^1.1.1", "intl-tel-input": "^25.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-international-phone": "^4.6.0", "react-router-dom": "^7.5.1", "react-router-hash-link": "^2.4.3", "tailwindcss": "^4.1.4", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.3.3", "@types/react-router-hash-link": "^2.4.9", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}