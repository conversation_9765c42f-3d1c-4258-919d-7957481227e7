@import "tailwindcss";
@font-face {
  font-family: GE_SS_Two_Light;
  src: url('/fonts/GE_SS_Two_Light.otf');
}
@theme {
  --color-btnColor: #4B2C53;
  --color-bgHomeHeroImage: #EBEBEB;
  --color-textbtnColor:#B38AC0;
  --color-authbtnActive: #CB88BB40;
  --bg-hero-image: url("/assets/hero-image.svg");
  --bg-main-color: #3B2241;
  --breakpoint-xl: 90rem !important;  /* 1440px */
  --breakpoint-5xl: 3840px !important;  /* 1440px */
  --breakpoint-4xl: 2560px !important;  /* 1440px */
  --breakpoint-3xl: 1920px !important;  /* 1440px */
  --breakpoint-laptop: 80rem;   /* 1280px */

}

.btn-primary {
  @apply 
    flex 
    justify-center 
    items-center
    text-center
    2xl:text-2xl xl:text-lg lg:text-sm md:text-sm text-xs
    font-normal  
    xl:px-8 lg:px-5 lg:py-3 md:p-2.5 p-1.5
    rounded-sm ;
}
.padding-global
{
  @apply
  2xl:px-[6.553%] 
  xl:px-[7.93%]
  md:px-[7.3%]
  px-7
}
.margin-global
{
  @apply 
  2xl:mx-[6.553%]
  xl:mx-[7.93%] 
  md:mx-[7.3%] 
  mx-7
}
.reset
{
  background-image: url(../src/assets/authImage/reset-1.svg);
  background-size: cover;
}
* {
    box-sizing: border-box;
    direction: rtl;
}
body {
  background-color: var(--bg-main-color);
  background-image: var(--bg-hero-image);
  background-position: top center;
  background-repeat: no-repeat;
  font-family: "GE_SS_Two_Light", sans-serif;

}
