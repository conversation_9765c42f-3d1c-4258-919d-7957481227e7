@import "tailwindcss";
@theme {
  --color-btnColor: #4B2C53;
  --color-bgHomeHeroImage: #EBEBEB;
  --color-textbtnColor:#B38AC0;
  --color-authbtnActive: #CB88BB40;
  --bg-hero-image: url("/assets/hero-image.svg");
  --bg-main-color: #3B2241;
  --breakpoint-xl: 90rem !important;  /* 1440px */
  --breakpoint-laptop: 80rem;   /* 1280px */

}
.btn-primary {
  @apply 
    flex 
    justify-center 
    items-center
    text-center
    xl:text-2xl lg:text-lg text-sm
    font-normal  
    lg:px-8 px-5 py-3
    rounded-sm ;
}
.padding-global
{
  @apply
  2xl:px-[6.553%] 
  xl:px-[7.93%]
  md:px-[7.3%]
  px-8 
}
.margin-global
{
  @apply 
  2xl:mx-[6.553%]
  xl:mx-[7.93%] 
  md:mx-[7.3%] 
  mx-8
}

* {
    box-sizing: border-box;
    direction: rtl;
}
body {
  background-color: var(--bg-main-color);
  background-image: var(--bg-hero-image);
  background-position: top center;
  background-repeat: no-repeat;
}