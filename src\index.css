@import "tailwindcss";
@font-face {
  font-family: GE_SS_Two_Light;
  src: url('/fonts/GE_SS_Two_Light.otf');
}
@theme {
  --color-btnColor: #4B2C53;
  --color-bgHomeHeroImage: #EBEBEB;
  --color-textbtnColor:#B38AC0;
  --color-primary: #3B2241;
  --color-authbtnActive: #CB88BB40;
  --bg-hero-image: url("/assets/hero-image.svg");
  --bg-main-color: #3B2241;
  --breakpoint-xl: 90rem !important;  /* 1440px */
  --breakpoint-5xl: 3840px !important;  /* 1440px */
  --breakpoint-4xl: 2560px !important;  /* 1440px */
  --breakpoint-3xl: 1920px !important;  /* 1440px */
  --breakpoint-laptop: 80rem;   /* 1280px */

}

.btn-primary {
  @apply 
    flex 
    justify-center 
    items-center
    text-center
    2xl:text-2xl xl:text-lg lg:text-sm md:text-sm text-xs
    font-normal  
    xl:px-8 lg:px-5 lg:py-3 md:p-2.5 p-1.5
    rounded-sm ;
}
.padding-global
{
  @apply
  2xl:px-[6.553%] 
  xl:px-[7.93%]
  md:px-[7.3%]
  px-7
}
.margin-global
{
  @apply 
  2xl:mx-[6.553%]
  xl:mx-[7.93%] 
  md:mx-[7.3%] 
  mx-7
}
.reset
{
  background-image: url(../src/assets/authImage/reset-1.svg);
  background-size: cover;
}
* {
    box-sizing: border-box;
    direction: rtl;
}
body {
  background-color: var(--bg-main-color);
  background-image: var(--bg-hero-image);
  background-position: top center;
  background-repeat: no-repeat;
  font-family: "GE_SS_Two_Light", sans-serif;
  scroll-behavior: smooth;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسينات الصور للوضوح في الموبايل */
img {
  max-width: 100%;
  height: auto;
  /* تحسين وضوح الصور */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* منع التشويش */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* تحسين خاص للصور في الكاردات */
.services-card-image {
  /* تحسين الدقة */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  image-rendering: crisp-edges;
  /* تحسين الحدة والألوان */
  filter: contrast(1.15) brightness(1.05) saturate(1.1);
  /* منع التشويش أثناء التحويل */
  transform: translateZ(0);
}

/* تحسين للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .services-card-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-quality;
    filter: contrast(1.1) brightness(1.03) saturate(1.05);
  }
}

/* تحسين للموبايل */
@media (max-width: 768px) {
  .services-card-image {
    /* تحسين إضافي للموبايل */
    filter: contrast(1.25) brightness(1.1) saturate(1.2);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    /* تحسين الحدة */
    -webkit-filter: contrast(1.25) brightness(1.1) saturate(1.2) unsharp-mask(amount(1.5) radius(0.5) threshold(0));
  }

  /* تحسين النصوص للموبايل */
  .services-card h2 {
    font-size: 1.125rem !important;
    line-height: 1.2 !important;
  }

  .services-card p {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
  }

  /* تحسين الكارد للموبايل */
  .services-card {
    min-height: 350px !important;
  }
}

/* تحسين للشاشات الصغيرة جداً */
@media (max-width: 480px) {
  .services-card-image {
    /* تحسين أقوى للشاشات الصغيرة */
    filter: contrast(1.3) brightness(1.12) saturate(1.25);
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
  }

  .services-card {
    min-height: 320px !important;
  }

  .services-card h2 {
    font-size: 1rem !important;
  }

  .services-card p {
    font-size: 0.8rem !important;
  }
}
