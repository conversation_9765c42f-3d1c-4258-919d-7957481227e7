// تكوين البيئة
export const config = {
  // رابط API
  apiUrl: process.env.REACT_APP_API_URL || "https://api.nafs-baserah.com/api/",
  
  // البيئة الحالية
  environment: process.env.NODE_ENV || "development",
  
  // إعدادات التوكن
  token: {
    // مدة انتهاء صلاحية التوكن (بالدقائق)
    expirationTime: 15,
    // مفتاح التخزين في localStorage
    storageKey: "accessToken",
  },
  
  // إعدادات الأمان
  security: {
    // تفعيل HTTPS في الإنتاج
    enforceHttps: process.env.NODE_ENV === "production",
    // تفعيل console.log في التطوير فقط
    enableLogging: process.env.NODE_ENV === "development",
  },
  
  // إعدادات التطبيق
  app: {
    name: "نفس بصيرة",
    version: "1.0.0",
    // صفحات محمية تتطلب تسجيل دخول
    protectedRoutes: ["/dashboard", "/profile"],
    // صفحات الأدمن فقط
    adminRoutes: ["/dashboard"],
  },
};

// دالة للتحقق من البيئة
export const isDevelopment = () => config.environment === "development";
export const isProduction = () => config.environment === "production";

// دالة للحصول على رابط API
export const getApiUrl = () => config.apiUrl;

// دالة للتحقق من صحة التوكن
export const isTokenValid = (token: string | null): boolean => {
  if (!token) return false;
  
  try {
    // فك تشفير JWT للتحقق من انتهاء الصلاحية
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    
    return payload.exp > currentTime;
  } catch {
    return false;
  }
};

// دالة للتسجيل الآمن (فقط في التطوير)
export const secureLog = (message: string, data?: any) => {
  if (config.security.enableLogging) {
    console.log(`[${config.app.name}] ${message}`, data);
  }
};

// دالة للتحقق من المسارات المحمية
export const isProtectedRoute = (path: string): boolean => {
  return config.app.protectedRoutes.some(route => path.startsWith(route));
};

// دالة للتحقق من مسارات الأدمن
export const isAdminRoute = (path: string): boolean => {
  return config.app.adminRoutes.some(route => path.startsWith(route));
};
