// api/axios.ts
import axios from "axios";
import { getApiUrl, secureLog, config } from "../config/environment";

export const api = axios.create({
  baseURL: getApiUrl(),
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// إضافة interceptor للتوكن
api.interceptors.request.use(
  (requestConfig) => {
    const token = localStorage.getItem(config.token.storageKey);
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
      secureLog("Request sent with token", { url: requestConfig.url });
    }
    return requestConfig;
  },
  (error) => {
    secureLog("Request error", error);
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابة للتعامل مع انتهاء صلاحية التوكن
api.interceptors.response.use(
  (response) => {
    secureLog("Response received", { status: response.status, url: response.config.url });
    return response;
  },
  (error) => {
    secureLog("Response error", { status: error.response?.status, url: error.config?.url });

    if (error.response?.status === 401) {
      // إذا كان التوكن منتهي الصلاحية، مسح التوكن وإعادة توجيه للتسجيل
      localStorage.removeItem(config.token.storageKey);
      secureLog("Token expired, redirecting to login");

      // تجنب إعادة التوجيه المتكررة
      if (!window.location.pathname.includes('/auth/')) {
        window.location.href = '/auth/login';
      }
    }

    return Promise.reject(error);
  }
);