import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { login, signUp, getCurrentUser, logout, resetPass, resetPassword } from "../services/loginAPI";

export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { email: string; password: string }) => login(data),
    onSuccess: async (response) => {
      console.log("Login successful, response:", response);
      // بعد تسجيل الدخول، نعيد جلب بيانات المستخدم
      await queryClient.invalidateQueries({ queryKey: ["me"] });
      // إجبار إعادة جلب البيانات
      await queryClient.refetchQueries({ queryKey: ["me"] });
    },
  });
}

export function useSignUp() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { fullName: string; email: string; password: string; confirmPassword : string; phoneNumber: string; }) => signUp(data),
    onSuccess: async () => {
      // بعد التسجيل، نعيد جلب بيانات المستخدم
      await queryClient.invalidateQueries({ queryKey: ["me"] });
    },
  });
}


export function useCurrentUser() {
  const query = useQuery({
    queryKey: ["me"],
    queryFn: getCurrentUser,
    retry: (failureCount, error) => {
      console.log("useCurrentUser retry", { failureCount, error });
      return failureCount < 1; // محاولة واحدة فقط
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // تسجيل النتائج
  if (query.data) {
    console.log("Current user data:", query.data);
  }
  if (query.error) {
    console.log("Current user error:", query.error);
  }

  return query;
}

export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: logout,
    onSuccess: () => {
      // مسح جميع البيانات من الكاش
      queryClient.clear();
    },
  });
}

export function useForgotPassword() {
  return useMutation({
    mutationFn: (data: { email: string }) => resetPass(data),
  });
}

export function useResetPassword() {
  return useMutation({
    mutationFn: (data: {
      resetCode: string;
      email: string;
      password: string;
      confirmPassword: string;
    }) => resetPassword(data),
  });
}